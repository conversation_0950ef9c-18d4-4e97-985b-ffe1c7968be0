<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import DatasetApi from '@/api/llm/dataset.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const datasetId = ref(null)
const dataset = ref({})
const documents = ref([])
const loading = ref(false)
const uploadVisible = ref(false)
const uploadLoading = ref(false)
const fileList = ref([])
const uploadDescription = ref('')

const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

const columns = [
  {
    title: '文档名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '上传时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

const loadDataset = () => {
  if (datasetId.value) {
    DatasetApi.get(datasetId.value, {
      toast: {
        success: false
      }
    }).then(result => {
      dataset.value = result.data
    })
  }
}

const loadDocuments = () => {
  if (!datasetId.value) return
  
  loading.value = true
  DatasetApi.searchDocuments(
    pagination.value.pageSize,
    pagination.value.current - 1,
    datasetId.value,
    '', // 搜索关键词
    {
      toast: {
        success: false
      }
    }
  ).then(result => {
    documents.value = result.data.records || []
    pagination.value.total = result.data.total || 0
  }).finally(() => {
    loading.value = false
  })
}

const showUpload = () => {
  uploadVisible.value = true
  fileList.value = []
  uploadDescription.value = ''
}

const handleUpload = () => {
  if (fileList.value.length === 0) {
    FeedbackUtil.message('请选择要上传的文件', 'warning')
    return
  }

  uploadLoading.value = true
  const files = fileList.value.map(file => file.originFileObj || file)
  
  DatasetApi.uploadDocument(
    null, // bucket
    datasetId.value,
    uploadDescription.value,
    files,
    {
      toast: {
        success: true
      }
    }
  ).then(() => {
    uploadVisible.value = false
    loadDocuments()
  }).finally(() => {
    uploadLoading.value = false
  })
}

const removeDocument = (documentId) => {
  FeedbackUtil.modal('您即将删除该文档，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = DatasetApi.removeDocument(documentId, {
        showLoading: false
      })
      _promise.then(() => {
        loadDocuments()
      })
      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

const goBack = () => {
  proxy.$router.push({
    name: 'llm.dataset.index'
  })
}

onMounted(() => {
  datasetId.value = proxy.$route.query.id
  if (datasetId.value) {
    loadDataset()
    loadDocuments()
  }
})
</script>

<template>
  <div class="dataset-documents">
    <div class="page-header">
      <div class="page-title">
        <folder-open-outlined class="title-icon" />
        {{ dataset.title || '知识库' }} - 文档管理
      </div>
      <div class="page-description">
        管理知识库中的文档，支持上传、删除等操作
      </div>
    </div>

    <a-card class="documents-card">
      <template #title>
        <a-space>
          <file-text-outlined />
          文档列表
        </a-space>
      </template>
      
      <template #extra>
        <a-space>
          <a-button
            type="primary"
            @click="showUpload"
          >
            <upload-outlined />
            上传文档
          </a-button>
          <a-button @click="goBack">
            <arrow-left-outlined />
            返回
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="documents"
        :loading="loading"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
        }"
        @change="(pag) => {
          pagination.current = pag.current
          pagination.pageSize = pag.pageSize
          loadDocuments()
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                danger
                @click="removeDocument(record.id)"
              >
                <delete-outlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 上传文档对话框 -->
    <a-modal
      v-model:open="uploadVisible"
      title="上传文档"
      :confirm-loading="uploadLoading"
      @ok="handleUpload"
    >
      <a-form layout="vertical">
        <a-form-item label="选择文件">
          <a-upload
            v-model:file-list="fileList"
            :before-upload="() => false"
            multiple
            accept=".txt,.pdf,.doc,.docx,.md"
          >
            <a-button>
              <upload-outlined />
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>
        
        <a-form-item label="描述（可选）">
          <a-textarea
            v-model:value="uploadDescription"
            placeholder="请输入文档描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.dataset-documents {
  padding: @padding-sm;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  padding: 24px 0;
  text-align: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16px;

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .title-icon {
      font-size: 32px;
    }
  }

  .page-description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
  }
}

.documents-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

// 响应式优化
@media (max-width: 768px) {
  .dataset-documents {
    padding: 16px;
  }

  .page-header {
    padding: 20px 16px;

    .page-title {
      font-size: 24px;

      .title-icon {
        font-size: 28px;
      }
    }

    .page-description {
      font-size: 14px;
    }
  }
}
</style>
