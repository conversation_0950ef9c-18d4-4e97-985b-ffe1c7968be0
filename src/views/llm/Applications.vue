<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import ApplicationApi from '@/api/llm/application.js'
import FeedbackUtil from '@/utils/feedback.js'
import GenericIcon from '@/components/GenericIcon.vue'

const { proxy } = getCurrentInstance()

const records = ref([])

const load = () => {
  ApplicationApi.me({
    toast: {
      success: false
    }
  }).then(result => {
    records.value = result.data
  })
}

const enter = record => {
  ApplicationApi.installId(record.platformId, {
    toast: {
      success: false
    }
  }).then(result => {
    proxy.$router.push({
      name: 'llm.application.home',
      query: {
        id: result.data
      }
    })
  })
}

const edit = record => {
  proxy.$router.push({
    name: 'llm.application.edit',
    query: record === null
      ? {}
      : {
          id: record.platformId
        }
  })
}

const remove = id => {
  FeedbackUtil.modal('您即将删除该记录，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = ApplicationApi.remove(id, {
        showLoading: false
      })
      _promise.then(() => {
        load()
      })

      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <div class="container">
    <a-row :gutter="[16, 16]">
      <!-- 新建应用卡片 -->
      <a-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="create-card"
          :hoverable="true"
          @click="edit(null)"
        >
          <div class="create-card-content">
            <div class="create-icon-wrapper">
              <plus-outlined class="create-icon" />
            </div>
            <h3 class="create-title">
              新建应用
            </h3>
            <p class="create-description">
              创建一个新的工作流应用
            </p>
          </div>
        </a-card>
      </a-col>

      <!-- 应用卡片列表 -->
      <a-col
        v-for="i in records"
        :key="i.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="app-card"
          :hoverable="true"
        >
          <div class="app-card-header">
            <div class="app-avatar-wrapper">
              <a-avatar
                :size="48"
                class="app-avatar"
              >
                <GenericIcon :icon="'icon-robot-2-line'" />
              </a-avatar>
            </div>
          </div>

          <div class="app-card-content">
            <h3
              class="app-title"
              :title="i.name || '未命名'"
            >
              {{ i.name || '未命名' }}
            </h3>
            <p
              class="app-description"
              :title="i.description || '暂无描述'"
            >
              {{ i.description || '暂无描述' }}
            </p>
            <div class="app-meta">
              <a-space :size="8">
                <a-tag
                  color="blue"
                  size="small"
                >
                  <tag-outlined />
                  暂无标签
                </a-tag>
              </a-space>
            </div>
          </div>

          <div class="app-card-actions">
            <a-tooltip title="开始对话">
              <a-button
                type="primary"
                shape="circle"
                size="small"
                class="action-btn primary-btn"
                @click="enter(i)"
              >
                <comment-outlined />
              </a-button>
            </a-tooltip>

            <a-tooltip title="编辑应用">
              <a-button
                type="default"
                shape="circle"
                size="small"
                class="action-btn"
                @click="edit(i)"
              >
                <edit-outlined />
              </a-button>
            </a-tooltip>

            <a-tooltip title="删除应用">
              <a-button
                type="default"
                shape="circle"
                size="small"
                class="action-btn danger-btn"
                @click="remove(i.id)"
              >
                <delete-outlined />
              </a-button>
            </a-tooltip>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.container {
  padding: @padding-sm;
  min-height: calc(100vh - 64px);
}

// 骨架屏卡片
.skeleton-card {
  height: 280px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  :deep(.ant-card-body) {
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
}

// 新建应用卡片
.create-card {
  height: 280px;
  border-radius: 16px;
  border: 2px dashed #d9d9d9;
  background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: #1890ff;
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(24, 144, 255, 0.2);

    &::before {
      opacity: 0.05;
    }

    .create-icon {
      transform: scale(1.1) rotate(90deg);
      color: #1890ff;
    }

    .create-title {
      color: #1890ff;
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .create-card-content {
    text-align: center;
    position: relative;
    z-index: 1;
  }

  .create-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .create-icon {
    font-size: 32px;
    color: #8c8c8c;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .create-title {
    font-size: 18px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    transition: color 0.3s ease;
  }

  .create-description {
    font-size: 14px;
    color: #8c8c8c;
    margin: 0;
    line-height: 1.5;
  }
}

// 应用卡片
.app-card {
  height: 280px;
  border-radius: 16px;
  background: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1890ff, #722ed1, #13c2c2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-6px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);

    &::before {
      transform: scaleX(1);
    }

    .app-avatar {
      transform: scale(1.1);
      box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
    }

    .app-card-actions {
      opacity: 1;
      transform: translateY(0);
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
  }

  .app-card-header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .app-avatar-wrapper {
    position: relative;
    display: inline-block;
  }

  .app-avatar {
    background: linear-gradient(135deg, #fa8c16, #faad14);
    color: #fff;
    font-size: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(250, 140, 22, 0.3);
  }

  .app-card-content {
    flex: 1;
    text-align: center;
    margin-bottom: 20px;
  }

  .app-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
  }

  .app-description {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 36px;
  }

  .app-meta {
    margin-bottom: 8px;

    :deep(.ant-tag) {
      margin: 0;
      border-radius: 12px;
      font-size: 11px;
      padding: 2px 8px;
      display: inline-flex;
      align-items: center;
      gap: 4px;

      .anticon {
        font-size: 10px;
      }
    }
  }

  .app-card-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    opacity: 0.7;
    transform: translateY(4px);
    transition: all 0.3s ease;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &.primary-btn {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border-color: #1890ff;
      color: #fff;

      &:hover {
        background: linear-gradient(135deg, #40a9ff, #1890ff);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
      }
    }

    &.danger-btn:hover {
      border-color: #ff4d4f;
      color: #ff4d4f;
      box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
    }
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.05;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.1;
  }
}

// 响应式优化
@media (max-width: 768px) {
  .applications-container {
    padding: 16px;
  }

  .page-header {
    padding: 24px 16px;
    margin-bottom: 24px;

    .page-title {
      font-size: 24px;

      .title-icon {
        font-size: 28px;
      }
    }

    .page-description {
      font-size: 14px;
    }
  }

  .app-card,
  .create-card,
  .skeleton-card {
    height: 240px;
  }

  .create-icon-wrapper {
    width: 60px;
    height: 60px;
    margin-bottom: 16px;
  }

  .create-icon {
    font-size: 24px;
  }

  .create-title {
    font-size: 16px;
  }

  .app-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px;
  }

  .app-title {
    font-size: 15px;
  }

  .app-description {
    font-size: 12px;
  }

  .empty-state {
    padding: 40px 16px;
    margin-top: 24px;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-card,
.create-card {
  animation: fadeInUp 0.6s ease-out;
}

// 为不同的卡片添加延迟动画
.app-card:nth-child(1) { animation-delay: 0.1s; }
.app-card:nth-child(2) { animation-delay: 0.2s; }
.app-card:nth-child(3) { animation-delay: 0.3s; }
.app-card:nth-child(4) { animation-delay: 0.4s; }
.app-card:nth-child(5) { animation-delay: 0.5s; }
.app-card:nth-child(6) { animation-delay: 0.6s; }
</style>
