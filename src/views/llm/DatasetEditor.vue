<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import DatasetApi from '@/api/llm/dataset.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const form = ref()
const loading = ref(false)
const record = ref({
  id: null,
  title: '',
  description: ''
})

const rules = {
  title: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { max: 100, message: '知识库名称不能超过100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ]
}

const load = () => {
  const id = proxy.$route.query.id
  if (id) {
    loading.value = true
    DatasetApi.get(id, {
      toast: {
        success: false
      }
    }).then(result => {
      record.value = result.data
    }).finally(() => {
      loading.value = false
    })
  }
}

const save = () => {
  form.value.validate().then(() => {
    loading.value = true
    DatasetApi.save(record.value, {
      toast: {
        success: true
      }
    }).then(() => {
      proxy.$router.push({
        name: 'llm.dataset.index'
      })
    }).finally(() => {
      loading.value = false
    })
  })
}

const cancel = () => {
  proxy.$router.push({
    name: 'llm.dataset.index'
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <div class="dataset-editor">
    <div class="page-header">
      <div class="page-title">
        <database-outlined class="title-icon" />
        {{ record.id ? '编辑知识库' : '新建知识库' }}
      </div>
      <div class="page-description">
        {{ record.id ? '修改知识库信息' : '创建一个新的知识库' }}
      </div>
    </div>

    <a-card class="form-card">
      <a-spin :spinning="loading">
        <a-form
          ref="form"
          :model="record"
          :rules="rules"
          layout="vertical"
          class="dataset-form"
        >
          <a-form-item
            label="知识库名称"
            name="title"
          >
            <a-input
              v-model:value="record.title"
              placeholder="请输入知识库名称"
              :maxlength="100"
              show-count
            />
          </a-form-item>

          <a-form-item
            label="描述"
            name="description"
          >
            <a-textarea
              v-model:value="record.description"
              placeholder="请输入知识库描述（可选）"
              :rows="4"
              :maxlength="500"
              show-count
            />
          </a-form-item>

          <a-form-item class="form-actions">
            <a-space>
              <a-button
                type="primary"
                :loading="loading"
                @click="save"
              >
                <save-outlined />
                保存
              </a-button>
              <a-button @click="cancel">
                <close-outlined />
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-card>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.dataset-editor {
  padding: @padding-sm;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
  padding: 24px 0;
  text-align: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16px;

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .title-icon {
      font-size: 32px;
    }
  }

  .page-description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
  }
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.dataset-form {
  padding: 24px;

  .form-actions {
    margin-top: 32px;
    text-align: center;
  }
}

// 响应式优化
@media (max-width: 768px) {
  .dataset-editor {
    padding: 16px;
  }

  .page-header {
    padding: 20px 16px;

    .page-title {
      font-size: 24px;

      .title-icon {
        font-size: 28px;
      }
    }

    .page-description {
      font-size: 14px;
    }
  }

  .dataset-form {
    padding: 16px;
  }
}
</style>
