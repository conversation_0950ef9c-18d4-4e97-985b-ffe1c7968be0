<script setup>
import { computed, ref, watch } from 'vue'
import jquery from 'jquery'
import GenericIcon from '@/components/GenericIcon.vue'
import MicrosoftAudioRecorder from '@/components/llm/MicrosoftAudioRecorder.vue'
import MediaApi from '@/api/media/record.js'

const props = defineProps({
  // 支持模型
  models: {
    type: Object,
    default: () => {}
  },
  // 支持插件
  plugins: {
    type: Array,
    default: () => []
  },
  // RAG附件类型
  accept: {
    type: String,
    default: ''
  },
  // 是否多附件
  multiple: {
    type: Boolean,
    default: false
  },
  // 语音配置
  speechConfig: {
    type: Object,
    default: null
  },
  send: {
    type: Function,
    default: (modelName, plugins, { id, file }, message) => {
      return Promise.resolve()
    }
  },
  uploadMedia: {
    type: Function,
    default: file => {
      return Promise.resolve({
        code: 'ok',
        data: [{
          id: new Date().getTime()
        }]
      })
    }
  },
  removeMedia: {
    type: Function,
    default: file => {
      return Promise.resolve({
        code: 'ok'
      })
    }
  },
  uploadDocument: {
    type: Function,
    default: file => {
      return Promise.resolve({
        code: 'ok',
        data: [{
          id: new Date().getTime()
        }]
      })
    }
  },
  removeDocument: {
    type: Function,
    default: file => {
      return Promise.resolve({
        code: 'ok'
      })
    }
  }
})

const mode = ref('default')
const switchMode = () => {
  mode.value = mode.value === 'default' ? 'reasoning' : 'default'
  modelName.value = models.value.length > 0 ? models.value[0].name : ''
}

const models = computed(() => {
  return props.models[mode.value]
})

const modelName = ref('')
watch(
  () => models.value,
  val => {
    if (modelName.value === '') {
      modelName.value = val.length > 0 ? val[0].name : ''
    }
  }, {
    immediate: true
  }
)

const plugins = ref([])
plugins.value = props.plugins.map((i, index) => {
  return index === 0 && i === null
    ? null
    : {
        ...i,
        _select: false
      }
})

const recording = ref(false)

const medias = ref({
  files: []
})

const multimodal = ref()

const clickMedia = () => {
  multimodal.value.$el.click()
}

const uploadMedia = ({ file }) => {
  const _uid = file.uid

  props.uploadMedia(file)
    .then(result => {
      medias.value.files
        .filter(i => i.uid === _uid)
        .forEach(i => {
          i.id = result.data[0].id
          i.url = MediaApi.preview(result.data[0].id)
          i.status = 'done'
        })
    })
    .catch(error => {
      medias.value.files
        .filter(i => i.uid === _uid)
        .forEach(i => {
          i.status = 'error'
          i.response = error.message
        })
    })
}

const removeMedia = file => {
  return file.id
    ? new Promise((resolve, reject) => {
      props.removeMedia(file)
        .then(() => {
          resolve()
        }).catch(error => {
          reject(error)
        })
    })
    : Promise.resolve()
}

const documents = ref({
  show: false,
  files: []
})

const setDocuments = files => {
  documents.value.files = Array.isArray(files)
    ? files.map(i => {
      return {
        id: i.id,
        name: i.name,
        status: 'done'
      }
    })
    : []
}

const uploadDocument = ({ action, data, file, filename, headers, onError, onProgress, onSuccess, withCredentials }) => {
  const _uid = file.uid

  props.uploadDocument(file)
    .then(result => {
      documents.value.files
        .filter(i => i.uid === _uid)
        .forEach(i => {
          i.id = result.data[0].id
          i.status = 'done'
        })
    })
    .catch(error => {
      documents.value.files
        .filter(i => i.uid === _uid)
        .forEach(i => {
          i.status = 'error'
          i.response = error.message
        })
    })
}

const removeDocument = file => {
  return file.id
    ? new Promise((resolve, reject) => {
      props.removeDocument(file)
        .then(() => {
          resolve()
        }).catch(error => {
          reject(error)
        })
    })
    : Promise.resolve()
}

const downloadDocument = file => {
  MediaApi.download(file.id)
}

// 当前消息id
let messageId = null

const input = ref('')
const setInput = value => {
  input.value = value
}

// 语音识别
const recognizing = word => {
  input.value += word
}

const STATUS = {
  READY: 'ready',
  RUNNING: 'running'
}
const status = ref(STATUS.READY)

const newLine = () => {
  input.value += '\n'
}

const getPlugins = () => {
  const _plugins = plugins.value
    .filter(i => i !== null && i._select)
    .map(i => i.id)

  // 存在文档则需要RAG
  if (documents.value.files.filter(i => i.status === 'done').length > 0) {
    _plugins.push('parser')
  }

  return _plugins
}

const send = event => {
  // 关闭附件
  documents.value.show = false

  // 关闭多模态
  const _media = medias.value.files.length > 0 ? medias.value.files[0] : null
  if (_media != null) {
    medias.value.files = []
  }

  // 关闭录音
  recording.value = false

  if (event && event.ctrlKey) {
    return
  }

  if (input.value.length === 0 || status.value !== STATUS.READY) {
    return
  }

  const _messageId = Date.now()
  messageId = _messageId

  status.value = STATUS.RUNNING

  props.send(modelName.value, getPlugins(), _media === null
    ? null
    : {
        id: _media.id,
        file: _media.originFileObj
      }
  , input.value)
    .finally(() => {
      // 当前消息id和发送消息id一致则更新状态
      if (_messageId === messageId) {
        status.value = STATUS.READY
      }
    })

  input.value = ''
}

defineExpose({
  setDocuments,
  setInput,
  send
})

const emits = defineEmits(['stop'])
const stop = () => {
  status.value = STATUS.READY

  emits('stop')
}
</script>

<template>
  <div class="sender">
    <div>
      <!-- 附件 -->
      <template v-if="documents.show">
        <a-upload-dragger
          v-model:file-list="documents.files"
          :accept="props.accept"
          :custom-request="uploadDocument"
          :multiple="props.multiple"
          :show-upload-list="true"
          @remove="removeDocument"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">
            拖拽文件至此，或者点击上传
          </p>
          <p class="ant-upload-hint">
            <slot name="upload-hint">
              {{ typeof props.accept === 'string' ? `仅支持${props.accept.split(',').join('，')}，文件大小不超过50MB。` : '' }}
            </slot>
          </p>

          <!-- 下载 -->
          <template #iconRender="{ file }">
            <download-outlined
              style="color: #1890ff"
              @click="downloadDocument(file)"
            />
          </template>
        </a-upload-dragger>
      </template>

      <!-- 输入 -->
      <div>
        <!-- ACTION -->
        <a-space style="padding-bottom: 12px">
          <!-- 插件 -->
          <template
            v-for="(i, index) in plugins"
            :key="index"
          >
            <template v-if="index === 0 && i === null">
              <a-button
                :type="mode === 'reasoning' ? 'primary' : 'default'"
                @click="switchMode"
              >
                <template #icon>
                  <GenericIcon :icon="'BulbOutlined'" />
                </template>
                深度思考
              </a-button>
            </template>

            <template v-else>
              <a-button
                :type="i._select ? 'primary' : 'default'"
                @click="i._select = !i._select"
              >
                <template #icon>
                  <GenericIcon :icon="i.icon" />
                </template>

                {{ i.title }}
              </a-button>
            </template>
          </template>
        </a-space>

        <!-- 多模态 -->
        <template v-if="jquery.isFunction(props.uploadMedia)">
          <a-upload
            v-model:file-list="medias.files"
            :accept="'image/*'"
            :custom-request="uploadMedia"
            :list-type="'picture'"
            :multiple="false"
            :show-upload-list="true"
            @remove="removeMedia"
          >
            <a-button
              ref="multimodal"
              style="display: none"
            />
          </a-upload>
        </template>

        <div class="input">
          <a-textarea
            v-model:value="input"
            :size="'large'"
            style="border: none"
            @keydown.enter.prevent="send"
            @keydown.ctrl.enter.prevent="newLine"
          />

          <a-space
            class="action"
            style="justify-content: space-between;width: 100%;"
          >
            <!-- 模型选择 -->
            <template v-if="props.models.default.length === 0 && props.models.reasoning.length === 0">
              <div />
            </template>
            <template v-else>
              <a-select
                v-model:value="modelName"
                :dropdown-match-select-width="false"
                :placement="'topLeft'"
              >
                <template
                  v-for="(i, index) in models"
                  :key="index"
                >
                  <a-select-option :value="i.name">
                    {{ i.title }}
                  </a-select-option>
                </template>
              </a-select>
            </template>

            <a-space>
              <!-- 上传文件 -->
              <template v-if="jquery.isFunction(props.uploadDocument)">
                <a-tooltip :title="'上传附件'">
                  <a-button
                    :type="'default'"
                    :shape="'circle'"
                    @click="documents.show = !documents.show"
                  >
                    <template #icon>
                      <paper-clip-outlined />
                    </template>
                  </a-button>
                </a-tooltip>
              </template>

              <!-- 发送多模态消息 -->
              <div v-if="!recording && medias.files.length === 0">
                <template v-if="jquery.isFunction(props.uploadMedia)">
                  <a-tooltip :title="'上传图片'">
                    <a-button :shape="'circle'">
                      <picture-outlined @click="clickMedia" />
                    </a-button>
                  </a-tooltip>
                </template>
              </div>

              <!-- 录音 -->
              <template v-if="props.speechConfig !== null && status === STATUS.READY">
                <a-tooltip :title="'语音输入'">
                  <MicrosoftAudioRecorder
                    :speech-key="props.speechConfig.key"
                    :service-region="props.speechConfig.region"
                    @start="recording = true"
                    @recognizing="recognizing"
                    @completed="send"
                  />
                </a-tooltip>
              </template>

              <!-- 发送文本消息 -->
              <div v-if="!recording">
                <!-- 就绪状态，可以发送 -->
                <template v-if="status === STATUS.READY">
                  <a-tooltip :title="'发送消息'">
                    <a-button
                      :type="'primary'"
                      :shape="'circle'"
                      :disabled="input.length === 0"
                      @click="send"
                    >
                      <template #icon>
                        <arrow-up-outlined />
                      </template>
                    </a-button>
                  </a-tooltip>
                </template>

                <!-- 其它状态，可以中止 -->
                <template v-else>
                  <a-tooltip :title="'中止发送'">
                    <a-button
                      :type="'link'"
                      :shape="'circle'"
                      class="btn-running"
                      @click="stop"
                    >
                      <template #icon>
                        <svg
                          color="currentColor"
                          viewBox="0 0 1000 1000"
                          xmlns="http://www.w3.org/2000/svg"
                          class="ant-sender-actions-btn-loading-icon"
                        >
                          <rect
                            fill="currentColor"
                            height="250"
                            rx="24"
                            ry="24"
                            width="250"
                            x="375"
                            y="375"
                          />
                          <circle
                            cx="500"
                            cy="500"
                            fill="none"
                            r="450"
                            stroke="currentColor"
                            stroke-width="100"
                            opacity="0.45"
                          />
                          <circle
                            cx="500"
                            cy="500"
                            fill="none"
                            r="450"
                            stroke="currentColor"
                            stroke-width="100"
                            stroke-dasharray="600 9999999"
                          >
                            <animateTransform
                              attributeName="transform"
                              dur="1s"
                              from="0 500 500"
                              repeatCount="indefinite"
                              to="360 500 500"
                              type="rotate"
                            />
                          </circle>
                        </svg>
                      </template>
                    </a-button>
                  </a-tooltip>
                </template>
              </div>
            </a-space>
          </a-space>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';
@import '@/global';

.ant-select {
  width: 120px;
}

.sender {
  position: absolute;
  bottom: 0;
  padding-top: @padding-sm;
  width: 100%;
  background-color: #ffffff;

  .ant-upload-hint {
    margin: @margin-sm;
    word-break: break-word;
  }

  :deep(.ant-upload-list) {
    margin-bottom: @margin-sm;

    [class*=ant-upload-list-item-] {
      .ant-upload-list-item-name {
        color: @primary-color;
      }
    }
  }

  :deep(.ant-upload-list-picture) {
    .ant-upload-list-item-done:first-child {
      margin-top: 0;
    }

    .ant-upload-list-item-done:last-child {
      margin-bottom: @margin-sm;
    }

    .ant-upload-list-item-error:first-child {
      margin-top: 0;
    }

    .ant-upload-list-item-error:last-child {
      margin-bottom: @margin-sm;
    }

    margin-bottom: 0;
  }

  .input {
    border-radius: 8px;
    background-color: @backgroup-color-base;

    textarea {
      border-radius: 8px;
      background-color: inherit;
      resize: none;
    }

    textarea:focus {
      box-shadow: none;
    }

    .action {
      padding: @padding-sm;

      .ant-select {
        width: auto;
        min-width: auto;

        .ant-select-selector {
          box-shadow: none !important;
          border: none;
          padding: 0;
          background-color: inherit;

          .ant-select-selection-item {
            color: @primary-color;
            text-decoration: underline;
          }
        }
      }
    }
  }

  .ant-btn-primary:disabled {
    border-color: @blue-3;
    color: @white;
    background-color: @blue-3;
  }

  :deep(.ant-space-item) {
    height: 32px;
  }

  .btn-running {
    padding: 0;
    color: @primary-color;

    .ant-sender-actions-btn-loading-icon {
      height: 32px;
      width: 32px;
    }
  }
}
</style>
