<script setup>
import { inject, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { Grid } from 'ant-design-vue'
import jquery from 'jquery'
import Conversations from '@/components/llm/Conversations.vue'
import Messages from '@/components/llm/Messages.vue'
import Prompts from '@/components/llm/Prompts.vue'
import Sender from '@/components/llm/Sender.vue'
import ConversationApi from '@/api/llm/conversation.js'

const props = defineProps({
  // 对话信息
  conversation: {
    type: Object,
    default: () => {
      return {
        id: '',
        title: ''
      }
    }
  },

  // 推荐问题
  questions: {
    type: Array,
    default: () => []
  },

  // 支持模型
  models: {
    type: Object,
    default: () => {
      return {
        default: [],
        reasoning: []
      }
    }
  },

  // 支持插件
  plugins: {
    type: Array,
    default: () => [null, {
      id: 'search',
      icon: 'GlobalOutlined',
      title: '联网搜索'
    }]
  },

  // 消息列表
  messages: {
    type: Array,
    default: () => []
  },

  // RAG附件列表
  documents: {
    type: Array,
    default: () => []
  },

  speechConfig: {
    type: Object,
    default: () => null
  },

  // 支持RAG附件
  accept: {
    type: String,
    default: ''
  },

  findConversations: {
    type: Function,
    // eslint-disable-next-line prefer-promise-reject-errors
    default: () => Promise.reject()
  },

  renameConversation: {
    type: Function,
    // eslint-disable-next-line prefer-promise-reject-errors
    default: (conversation, title) => Promise.reject()
  },

  removeConversation: {
    type: Function,
    // eslint-disable-next-line prefer-promise-reject-errors
    default: conversation => Promise.reject()
  },

  clearMemory: {
    type: Function,
    // eslint-disable-next-line prefer-promise-reject-errors
    default: () => Promise.reject()
  },

  send: {
    type: Function,
    default: (modelName, plugins, mediaId, message, onopen, onmessage, onclose, onerror) => {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject()
    }
  },

  uploadMedia: {
    type: Function,
    default: file => {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject()
    }
  },

  removeMedia: {
    type: Function,
    default: file => {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject()
    }
  },

  uploadDocument: {
    type: Function,
    default: file => {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject()
    }
  },

  removeDocument: {
    type: Function,
    default: file => {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject()
    }
  }
})

const conversations = ref(null)

const messages = ref(null)

const sender = ref(null)

watch(
  () => props.messages,
  () => {
    messages.value.set(props.messages)

    nextTick(() => {
      scrollToBottom()
    })
  }
)

watch(
  () => props.documents,
  () => {
    sender.value.setDocuments(props.documents)

    nextTick(() => {
      scrollToBottom()
    })
  }
)

const breakpoint = Grid.useBreakpoint()

const emits = defineEmits(['goto', 'newConversation'])

// 打开对话列表
const open = ref(false)

const suggest = question => {
  sender.value.setInput(question.desc)
}

// 发送文本消息
const send = (modelName, plugins, media, message) => {
  const { id, onopen, onmessage, onclose, onerror } = messages.value.ask(message, media === null ? null : media.file)
  signal.messageId = id
  scrollToBottom()

  return new Promise((resolve, reject) => {
    props.send(modelName, plugins, media === null ? null : media.id, message, event => {
      onopen(event)
    }, event => {
      onmessage(event)

      scrollToBottom()
    }, event => {
      if (typeof props.conversation.title !== 'string' || props.conversation.title.length === 0) {
        ConversationApi.rename(props.conversation.id, null, {
          showLoading: false,
          toast: {
            success: false,
            error: false
          }
        }).then(() => {
          if (open.value) {
            conversations.value.load()
          }
        })
      }

      onclose(event)

      resolve()
    }, error => {
      onerror(error)

      // eslint-disable-next-line prefer-promise-reject-errors
      reject({
        message: error.message
      })
    })
      .then(controller => {
        signal.controller = controller
      })
      .catch(error => {
        reject(error)
      })
  })
}

// 中止
const signal = {
  messageId: null,
  controller: null
}
const stop = () => {
  if (signal.controller !== null) {
    signal.controller.abort()
  }

  if (signal.messageId !== null) {
    messages.value.abort(signal.messageId)
  }
}

// 重试
const retry = text => {
  sender.value.setInput(text)
  sender.value.send()
}

// 清理记忆
const reloadPage = inject('reloadPage')
const clearMemory = () => {
  props.clearMemory()
    .then(() => {
      reloadPage()
    })
}

// 滚动到底部
const autoScroll = ref(true)

// 监听滚动事件
const onScroll = () => {
  autoScroll.value = window.innerHeight + window.pageYOffset >= document.body.offsetHeight - 10
}

const scrollToBottom = () => {
  if (autoScroll.value) {
    nextTick(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })
  }
}

onMounted(() => {
  // 添加滚动事件监听
  window.addEventListener('scroll', onScroll)
})

onUnmounted(() => {
  // 移除滚动事件监听
  window.removeEventListener('scroll', onScroll)
})
</script>

<template>
  <a-row :justify="'center'">
    <a-col
      :xs="24"
      :sm="18"
      :lg="12"
    >
      <a-alert
        :type="'info'"
        :message="'Hello, I\'m Ant Design X'"
        :description="'Base on Ant Design, AGI product interface solution, create a better intelligent vision~'"
        :show-icon="true"
      >
        <template #icon>
          <img
            src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
            alt=""
          >
        </template>
      </a-alert>

      <Prompts
        :questions="props.questions"
        style="margin-bottom: 12px"
        @select="suggest"
      >
        <template #tip>
          <p style="margin: 0;">
            <a-space>
              <smile-outlined style="color: #fadb14;font-size: 22px" /> 您可以问我：
            </a-space>
          </p>
        </template>
      </Prompts>

      <Messages
        ref="messages"
        :style="plugins.length > 0 ? 'padding-bottom: 177px' : 'padding-bottom: 132px'"
        @retry="retry"
      />

      <Sender
        ref="sender"
        :models="props.models"
        :plugins="props.plugins"
        :accept="accept"
        :multiple="true"
        :speech-config="props.speechConfig"
        :send="send"
        :upload-media="props.uploadMedia"
        :remove-media="props.removeMedia"
        :upload-document="props.uploadDocument"
        :remove-document="props.removeDocument"
        @stop="stop"
      />
    </a-col>
  </a-row>

  <!-- 会话 -->
  <a-drawer
    v-model:open="open"
    :placement="'right'"
    :width="breakpoint.xs ? '200px' : '300px'"
  >
    <Conversations
      ref="conversations"
      :conversation="props.conversation"
      :load="props.findConversations"
      :rename="props.renameConversation"
      :remove="props.removeConversation"
      @new="emits('newConversation')"
      @select="item => {
        emits('goto', item)
      }"
    />
  </a-drawer>

  <!-- 悬浮按钮 -->
  <a-float-button-group
    :shape="'circle'"
    :style="breakpoint.xs ? {
      bottom: '160px'
    } : null"
  >
    <a-float-button
      :shape="'circle'"
      :type="'primary'"
      :tooltip="'会话'"
      @click="open = !open"
    >
      <template #icon>
        <menu-outlined />
      </template>
    </a-float-button>

    <template v-if="jquery.isFunction(props.clearMemory)">
      <a-float-button
        :shape="'circle'"
        :tooltip="'清除会话'"
        @click="clearMemory"
      >
        <template #icon>
          <delete-outlined />
        </template>
      </a-float-button>
    </template>
  </a-float-button-group>
</template>

<style lang="less" scoped>
@import '@/less/default';

.ant-row {
  flex-grow: 1;

  .ant-alert {
    margin-bottom: @margin-sm;

    img {
      height: 74px;
    }

    :deep(.ant-alert-message) {
      font-size: 28px;
      font-weight: bold;
    }
  }
}
</style>
