import HttpRequest from '@/utils/http.js'

export default {
  installId (difyId, options) {
    return new HttpRequest().ajax('/llm/application/get/install-id', {
      difyId
    }, options)
  },
  // 模糊搜索
  search (count, index, name, options) {
    return new HttpRequest().ajax('/llm/application/search', {
      count,
      index,
      name
    }, options)
  },
  me (options) {
    return new HttpRequest().ajax('/llm/application/me', null, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/llm/application/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/llm/application/remove', {
      id
    }, options)
  }
}
