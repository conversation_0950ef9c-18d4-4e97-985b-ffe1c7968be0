import HttpRequest from '@/utils/http.js'
import FeedbackUtil from '@/utils/feedback.js'

export default {
  get (id, options) {
    return new HttpRequest().ajax('/llm/dataset/get', {
      id
    }, options)
  },
  // 模糊搜索
  search (count, index, title, options) {
    return new HttpRequest().ajax('/llm/dataset/search', {
      count,
      index,
      title
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/llm/dataset/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/llm/dataset/remove', {
      id
    }, options)
  },
  // 模糊搜索文档
  searchDocuments (count, index, id, name, options) {
    return new HttpRequest().ajax('/llm/dataset/document/search', {
      count,
      index,
      id,
      name
    }, options)
  },
  // 上传文档
  uploadDocument (bucket, id, description, files, options) {
    if (typeof bucket === 'string') {
      bucket = bucket.replaceAll(/\\/g, '/')
    }

    const _data = new FormData()
    _data.append('datasetId', id)
    _data.append('description', description)

    files.forEach(i => {
      _data.append('files', i)
    })

    return new Promise((resolve, reject) => {
      new HttpRequest({
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-Requested-With': ''
        } /*,
      onUploadProgress: function (event) {
        const _rate = event.loaded * 100 / event.total
      } */
      }).ajax(typeof bucket === 'string' ? `/llm/dataset/document/upload?bucket=${bucket}` : '/llm/dataset/document/upload', _data, options)
        .then(result => {
          if (files.length === 1 && !result.data[0].success) {
            if (!options.toast || options.toast.error !== true) {
              FeedbackUtil.message(result.data[0].message, 'error')
            }

            // eslint-disable-next-line prefer-promise-reject-errors
            reject({
              message: result.data[0].message
            })
          } else {
            resolve(result)
          }
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 更新文档
  updateDocument (documentId, description, options) {
    return new HttpRequest().ajax('/llm/dataset/document/update', {
      documentId,
      description
    }, options)
  },
  // 删除文档
  removeDocument (documentId, options) {
    return new HttpRequest().ajax('/llm/dataset/document/remove', {
      documentId
    }, options)
  }
}
