{"name": "sparrow-antd-admin", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint --ext .js,.vue src/** --fix"}, "dependencies": {"@ant-design/icons-vue": "^7.0.0", "@microsoft/fetch-event-source": "^2.0.1", "aieditor": "^1.2.5", "ant-design-vue": "^4.x", "axios": "^1.4.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.11", "echarts": "^5.4.2", "exif-js": "^2.3.0", "jquery": "^3.7.1", "jsencrypt": "^3.3.2", "less": "^4.1.3", "marked": "^15.0.7", "microsoft-cognitiveservices-speech-sdk": "^1.43.1", "vue": "^3.4.15", "vue-router": "^4.2.1", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.1.0", "eslint": "8.22.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.14.1", "vite": "^4.3.2"}}